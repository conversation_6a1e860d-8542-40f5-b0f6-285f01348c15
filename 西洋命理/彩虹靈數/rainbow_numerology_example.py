#!/usr/bin/env python3
"""
彩虹靈數計算器 - 完整範例
Rainbow Numerology Calculator - Complete Example

這個腳本展示了如何使用Python進行彩虹靈數的計算、分析和視覺化。
包含了基本計算、色彩對應、圖表生成等功能。

作者: Rainbow Numerology Team
版本: 1.0.0
日期: 2024
"""

import datetime
import matplotlib.pyplot as plt
import numpy as np
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
import pandas as pd

@dataclass
class ColorInfo:
    """色彩信息數據類"""
    name: str
    hex_code: str
    rgb: Tuple[int, int, int]
    chakra: str
    element: str
    keywords: List[str]

class RainbowNumerologyCalculator:
    """彩虹靈數計算器主類"""
    
    def __init__(self):
        """初始化計算器"""
        self.letter_values = self._init_letter_values()
        self.vowels = set('AEIOU')
        self.consonants = set('BCDFGHJKLMNPQRSTVWXYZ')
        self.color_map = self._init_color_map()
    
    def _init_letter_values(self) -> Dict[str, int]:
        """初始化字母數值對應表"""
        values = {}
        letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
        for i, letter in enumerate(letters):
            values[letter] = (i % 9) + 1
        return values
    
    def _init_color_map(self) -> Dict[int, ColorInfo]:
        """初始化色彩對應表"""
        return {
            1: ColorInfo("紅色", "#FF0000", (255, 0, 0), "海底輪", "火", 
                        ["領導", "行動", "開創", "獨立", "勇氣"]),
            2: ColorInfo("橙色", "#FF8000", (255, 128, 0), "臍輪", "水", 
                        ["合作", "平衡", "感性", "和諧", "夥伴"]),
            3: ColorInfo("黃色", "#FFFF00", (255, 255, 0), "太陽神經叢", "火", 
                        ["創意", "表達", "樂觀", "溝通", "藝術"]),
            4: ColorInfo("綠色", "#00FF00", (0, 255, 0), "心輪", "土", 
                        ["穩定", "成長", "療癒", "務實", "建設"]),
            5: ColorInfo("藍色", "#0080FF", (0, 128, 255), "喉輪", "風", 
                        ["自由", "溝通", "變化", "冒險", "真理"]),
            6: ColorInfo("靛色", "#4000FF", (64, 0, 255), "眉心輪", "水", 
                        ["責任", "直覺", "服務", "關愛", "家庭"]),
            7: ColorInfo("紫色", "#8000FF", (128, 0, 255), "頂輪", "風", 
                        ["靈性", "智慧", "神秘", "內省", "研究"]),
            8: ColorInfo("粉色", "#FF80FF", (255, 128, 255), "心輪擴展", "土", 
                        ["力量", "成就", "慈悲", "權威", "物質"]),
            9: ColorInfo("金色", "#FFD700", (255, 215, 0), "全脈輪", "光", 
                        ["完成", "智慧", "服務", "博愛", "圓滿"])
        }
    
    def reduce_to_single_digit(self, number: int) -> int:
        """將數字簡化為個位數，保留主數11, 22, 33"""
        while number > 9 and number not in [11, 22, 33]:
            number = sum(int(digit) for digit in str(number))
        return number
    
    def calculate_life_number(self, birth_date: str) -> int:
        """
        計算生命數字
        
        Args:
            birth_date: 出生日期，格式為 'YYYY-MM-DD'
        
        Returns:
            生命數字 (1-9 或 11, 22, 33)
        """
        try:
            date_obj = datetime.datetime.strptime(birth_date, '%Y-%m-%d').date()
            date_string = date_obj.strftime('%Y%m%d')
            total = sum(int(digit) for digit in date_string)
            return self.reduce_to_single_digit(total)
        except ValueError:
            raise ValueError(f"無效的日期格式: {birth_date}，請使用 YYYY-MM-DD 格式")
    
    def calculate_personality_number(self, full_name: str) -> int:
        """
        計算個性數字（基於子音）
        
        Args:
            full_name: 完整姓名
        
        Returns:
            個性數字 (1-9 或 11, 22, 33)
        """
        clean_name = ''.join(char.upper() for char in full_name if char.isalpha())
        consonant_sum = sum(self.letter_values[char] for char in clean_name 
                           if char in self.consonants)
        return self.reduce_to_single_digit(consonant_sum)
    
    def calculate_soul_number(self, full_name: str) -> int:
        """
        計算靈魂數字（基於母音）
        
        Args:
            full_name: 完整姓名
        
        Returns:
            靈魂數字 (1-9 或 11, 22, 33)
        """
        clean_name = ''.join(char.upper() for char in full_name if char.isalpha())
        vowel_sum = sum(self.letter_values[char] for char in clean_name 
                       if char in self.vowels)
        return self.reduce_to_single_digit(vowel_sum)
    
    def calculate_destiny_number(self, life_number: int, personality_number: int) -> int:
        """計算使命數字（生命數字 + 個性數字）"""
        return self.reduce_to_single_digit(life_number + personality_number)
    
    def get_complete_profile(self, name: str, birth_date: str) -> Dict:
        """
        獲取完整的彩虹靈數檔案
        
        Args:
            name: 完整姓名
            birth_date: 出生日期 (YYYY-MM-DD)
        
        Returns:
            包含所有數字和色彩信息的字典
        """
        profile = {
            'name': name,
            'birth_date': birth_date,
            'life_number': self.calculate_life_number(birth_date),
            'personality_number': self.calculate_personality_number(name),
            'soul_number': self.calculate_soul_number(name)
        }
        
        # 計算使命數字
        profile['destiny_number'] = self.calculate_destiny_number(
            profile['life_number'], profile['personality_number']
        )
        
        # 添加色彩信息
        for key in ['life_number', 'personality_number', 'soul_number', 'destiny_number']:
            number = profile[key]
            profile[f'{key}_color'] = self.color_map[number]
        
        return profile
    
    def create_color_wheel(self, numbers: List[int], title: str = "彩虹靈數色彩輪") -> None:
        """
        創建色彩輪圖表
        
        Args:
            numbers: 數字列表
            title: 圖表標題
        """
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        # 計算角度
        angles = np.linspace(0, 2*np.pi, len(numbers), endpoint=False)
        
        # 繪製色彩扇形
        for i, (number, angle) in enumerate(zip(numbers, angles)):
            color_info = self.color_map[number]
            
            # 繪製扇形
            width = 2*np.pi / len(numbers)
            ax.bar(angle, 1, width=width, bottom=0.3, 
                  color=color_info.hex_code, alpha=0.8, 
                  edgecolor='white', linewidth=2)
            
            # 添加數字標籤
            ax.text(angle, 0.8, str(number), 
                   ha='center', va='center', 
                   fontsize=16, fontweight='bold', color='white')
            
            # 添加色彩名稱
            ax.text(angle, 0.5, color_info.name, 
                   ha='center', va='center', 
                   fontsize=10, color='white')
        
        ax.set_ylim(0, 1.2)
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        ax.set_rticks([])  # 隱藏徑向刻度
        ax.set_thetagrids([])  # 隱藏角度刻度
        
        plt.tight_layout()
        plt.show()
    
    def create_profile_chart(self, profile: Dict) -> None:
        """
        創建個人檔案圖表
        
        Args:
            profile: 個人檔案字典
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 數字條形圖
        numbers = [profile['life_number'], profile['personality_number'], 
                  profile['soul_number'], profile['destiny_number']]
        labels = ['生命數字', '個性數字', '靈魂數字', '使命數字']
        colors = [profile[f'{key}_color'].hex_code for key in 
                 ['life_number', 'personality_number', 'soul_number', 'destiny_number']]
        
        bars = ax1.bar(labels, numbers, color=colors, alpha=0.8, edgecolor='black')
        ax1.set_title(f'{profile["name"]} 的彩虹靈數檔案', fontsize=14, fontweight='bold')
        ax1.set_ylabel('數字值')
        ax1.set_ylim(0, 10)
        
        # 在條形上添加數字標籤
        for bar, number in zip(bars, numbers):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    str(number), ha='center', va='bottom', fontweight='bold')
        
        # 色彩圓餅圖
        ax2.pie([1]*len(numbers), colors=colors, labels=labels, autopct='%s',
               pctdistance=0.85, startangle=90)
        ax2.set_title('色彩分佈', fontsize=14, fontweight='bold')
        
        # 脈輪對應
        chakras = [profile[f'{key}_color'].chakra for key in 
                  ['life_number', 'personality_number', 'soul_number', 'destiny_number']]
        chakra_counts = pd.Series(chakras).value_counts()
        
        ax3.barh(chakra_counts.index, chakra_counts.values, 
                color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        ax3.set_title('脈輪分佈', fontsize=14, fontweight='bold')
        ax3.set_xlabel('出現次數')
        
        # 關鍵詞雲（簡化版）
        all_keywords = []
        for key in ['life_number', 'personality_number', 'soul_number', 'destiny_number']:
            all_keywords.extend(profile[f'{key}_color'].keywords)
        
        keyword_counts = pd.Series(all_keywords).value_counts().head(10)
        
        ax4.barh(keyword_counts.index, keyword_counts.values, 
                color=plt.cm.rainbow(np.linspace(0, 1, len(keyword_counts))))
        ax4.set_title('關鍵特質', fontsize=14, fontweight='bold')
        ax4.set_xlabel('出現次數')
        
        plt.tight_layout()
        plt.show()
    
    def print_detailed_analysis(self, profile: Dict) -> None:
        """
        打印詳細分析報告
        
        Args:
            profile: 個人檔案字典
        """
        print("=" * 60)
        print(f"🌈 {profile['name']} 的彩虹靈數完整分析報告")
        print("=" * 60)
        print(f"出生日期: {profile['birth_date']}")
        print()
        
        # 各個數字的詳細分析
        number_types = [
            ('life_number', '生命數字', '反映人生主要課題和發展方向'),
            ('personality_number', '個性數字', '展現外在表現和社交特質'),
            ('soul_number', '靈魂數字', '揭示內在渴望和靈性追求'),
            ('destiny_number', '使命數字', '指出人生終極目標和貢獻')
        ]
        
        for key, name, description in number_types:
            number = profile[key]
            color_info = profile[f'{key}_color']
            
            print(f"🔢 {name}: {number}")
            print(f"🎨 對應色彩: {color_info.name} ({color_info.hex_code})")
            print(f"🧘 相關脈輪: {color_info.chakra}")
            print(f"🌟 關鍵特質: {', '.join(color_info.keywords)}")
            print(f"📝 意義: {description}")
            print("-" * 40)
        
        # 整體分析
        print("🔍 整體分析:")
        self._generate_overall_analysis(profile)
        print("=" * 60)
    
    def _generate_overall_analysis(self, profile: Dict) -> None:
        """生成整體分析"""
        life_num = profile['life_number']
        personality_num = profile['personality_number']
        soul_num = profile['soul_number']
        
        # 數字和諧度分析
        if life_num == personality_num:
            print("✨ 生命數字與個性數字相同，表示內外一致，真實自然。")
        elif abs(life_num - personality_num) <= 2:
            print("🌟 生命數字與個性數字相近，內外較為協調。")
        else:
            print("🔄 生命數字與個性數字差異較大，需要平衡內在與外在表現。")
        
        # 色彩能量分析
        colors = [profile[f'{key}_color'].name for key in 
                 ['life_number', 'personality_number', 'soul_number']]
        
        warm_colors = ['紅色', '橙色', '黃色']
        cool_colors = ['綠色', '藍色', '靛色', '紫色']
        
        warm_count = sum(1 for color in colors if color in warm_colors)
        cool_count = sum(1 for color in colors if color in cool_colors)
        
        if warm_count > cool_count:
            print("🔥 暖色系能量較強，性格偏向外向、積極、行動導向。")
        elif cool_count > warm_count:
            print("❄️ 冷色系能量較強，性格偏向內向、理性、思考導向。")
        else:
            print("⚖️ 暖冷色彩平衡，具有很好的適應性和平衡感。")

def main():
    """主函數 - 示範程式使用"""
    print("🌈 歡迎使用彩虹靈數計算器！")
    print("=" * 50)
    
    # 創建計算器實例
    calculator = RainbowNumerologyCalculator()
    
    # 示範數據
    examples = [
        {"name": "張小明", "birth_date": "1985-03-15"},
        {"name": "李美華", "birth_date": "1990-07-22"},
        {"name": "王大偉", "birth_date": "1978-11-08"}
    ]
    
    for example in examples:
        print(f"\n正在分析 {example['name']} 的彩虹靈數...")
        
        try:
            # 計算完整檔案
            profile = calculator.get_complete_profile(
                example['name'], example['birth_date']
            )
            
            # 打印詳細分析
            calculator.print_detailed_analysis(profile)
            
            # 創建視覺化圖表
            numbers = [profile['life_number'], profile['personality_number'], 
                      profile['soul_number'], profile['destiny_number']]
            
            # 色彩輪圖表
            calculator.create_color_wheel(numbers, f"{example['name']} 的彩虹靈數色彩輪")
            
            # 個人檔案圖表
            calculator.create_profile_chart(profile)
            
        except Exception as e:
            print(f"❌ 計算過程中發生錯誤: {e}")
    
    print("\n🎉 分析完成！感謝使用彩虹靈數計算器！")

if __name__ == "__main__":
    # 設置中文字體支持（如果需要）
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    main()
