# 開源資源與套件

## 概述

開源資源是彩虹靈數Python開發的重要基礎，本文將介紹相關的開源套件、工具和資源，幫助開發者快速建立功能完整的彩虹靈數應用。

## 核心計算套件

### 數字學相關套件

#### numerology-py
```python
# 安裝
pip install numerology-py

# 基本使用
from numerology import Numerology

num = Numerology()
life_path = num.life_path_number("1985-03-15")
expression = num.expression_number("<PERSON>")
```

#### pythagorean-numerology
```python
# 安裝
pip install pythagorean-numerology

# 使用示例
from pythagorean_numerology import PythagoreanNumerology

pn = PythagoreanNumerology()
result = pn.calculate_all("<PERSON> Smith", "1985-03-15")
```

### 自製基礎套件

#### rainbow-numerology-core
```python
"""
彩虹靈數核心計算套件
GitHub: https://github.com/rainbow-numerology/core
"""

class RainbowNumerologyCore:
    """彩虹靈數核心計算類"""
    
    def __init__(self):
        self.letter_map = self._create_letter_map()
        self.color_map = self._create_color_map()
    
    def _create_letter_map(self):
        """創建字母數字對應表"""
        mapping = {}
        letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
        for i, letter in enumerate(letters):
            mapping[letter] = (i % 9) + 1
        return mapping
    
    def _create_color_map(self):
        """創建數字色彩對應表"""
        return {
            1: {'name': 'Red', 'hex': '#FF0000', 'chakra': 'Root'},
            2: {'name': 'Orange', 'hex': '#FF8000', 'chakra': 'Sacral'},
            3: {'name': 'Yellow', 'hex': '#FFFF00', 'chakra': 'Solar Plexus'},
            4: {'name': 'Green', 'hex': '#00FF00', 'chakra': 'Heart'},
            5: {'name': 'Blue', 'hex': '#0080FF', 'chakra': 'Throat'},
            6: {'name': 'Indigo', 'hex': '#4000FF', 'chakra': 'Third Eye'},
            7: {'name': 'Violet', 'hex': '#8000FF', 'chakra': 'Crown'},
            8: {'name': 'Pink', 'hex': '#FF80FF', 'chakra': 'Heart Extended'},
            9: {'name': 'Gold', 'hex': '#FFD700', 'chakra': 'All Chakras'}
        }
```

## 視覺化套件

### Matplotlib 擴展

#### rainbow-charts
```python
"""
彩虹靈數專用圖表套件
"""
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Circle
import matplotlib.patches as patches

class RainbowCharts:
    """彩虹靈數視覺化工具"""
    
    def __init__(self):
        self.colors = {
            1: '#FF0000', 2: '#FF8000', 3: '#FFFF00',
            4: '#00FF00', 5: '#0080FF', 6: '#4000FF',
            7: '#8000FF', 8: '#FF80FF', 9: '#FFD700'
        }
    
    def create_chakra_wheel(self, numbers, title="Chakra Energy Wheel"):
        """創建脈輪能量輪"""
        fig, ax = plt.subplots(figsize=(10, 10))
        
        # 創建圓形圖
        angles = np.linspace(0, 2*np.pi, len(numbers), endpoint=False)
        
        for i, (number, angle) in enumerate(zip(numbers, angles)):
            # 計算位置
            x = np.cos(angle)
            y = np.sin(angle)
            
            # 繪製圓圈
            circle = Circle((x, y), 0.3, 
                          color=self.colors[number], 
                          alpha=0.7)
            ax.add_patch(circle)
            
            # 添加數字標籤
            ax.text(x, y, str(number), 
                   ha='center', va='center', 
                   fontsize=20, fontweight='bold')
        
        ax.set_xlim(-2, 2)
        ax.set_ylim(-2, 2)
        ax.set_aspect('equal')
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.axis('off')
        
        return fig, ax
    
    def create_rainbow_bar(self, numbers, labels=None):
        """創建彩虹條形圖"""
        fig, ax = plt.subplots(figsize=(12, 6))
        
        if labels is None:
            labels = [f'Number {n}' for n in numbers]
        
        bars = ax.bar(range(len(numbers)), [1]*len(numbers), 
                     color=[self.colors[n] for n in numbers])
        
        # 添加數字標籤
        for i, (bar, number) in enumerate(zip(bars, numbers)):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height/2,
                   str(number), ha='center', va='center',
                   fontsize=16, fontweight='bold', color='white')
        
        ax.set_xticks(range(len(numbers)))
        ax.set_xticklabels(labels, rotation=45)
        ax.set_ylabel('Energy Level')
        ax.set_title('Rainbow Numerology Profile')
        
        return fig, ax
```

### Plotly 互動式圖表

#### rainbow-interactive
```python
"""
互動式彩虹靈數圖表
"""
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

class InteractiveRainbowCharts:
    """互動式彩虹靈數圖表"""
    
    def __init__(self):
        self.color_map = {
            1: '#FF0000', 2: '#FF8000', 3: '#FFFF00',
            4: '#00FF00', 5: '#0080FF', 6: '#4000FF',
            7: '#8000FF', 8: '#FF80FF', 9: '#FFD700'
        }
    
    def create_3d_energy_sphere(self, life_number, personality_number, soul_number):
        """創建3D能量球體"""
        fig = go.Figure()
        
        # 主要能量球（生命數字）
        fig.add_trace(go.Scatter3d(
            x=[0], y=[0], z=[0],
            mode='markers',
            marker=dict(
                size=30,
                color=self.color_map[life_number],
                opacity=0.8
            ),
            name=f'Life Number: {life_number}',
            text=f'Life Number: {life_number}'
        ))
        
        # 個性能量球
        fig.add_trace(go.Scatter3d(
            x=[2], y=[0], z=[0],
            mode='markers',
            marker=dict(
                size=20,
                color=self.color_map[personality_number],
                opacity=0.6
            ),
            name=f'Personality: {personality_number}',
            text=f'Personality: {personality_number}'
        ))
        
        # 靈魂能量球
        fig.add_trace(go.Scatter3d(
            x=[-2], y=[0], z=[0],
            mode='markers',
            marker=dict(
                size=20,
                color=self.color_map[soul_number],
                opacity=0.6
            ),
            name=f'Soul: {soul_number}',
            text=f'Soul: {soul_number}'
        ))
        
        fig.update_layout(
            title='3D Energy Sphere Visualization',
            scene=dict(
                xaxis_title='X Axis',
                yaxis_title='Y Axis',
                zaxis_title='Z Axis'
            )
        )
        
        return fig
    
    def create_radar_chart(self, numbers_dict):
        """創建雷達圖"""
        categories = list(numbers_dict.keys())
        values = list(numbers_dict.values())
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=categories,
            fill='toself',
            name='Numerology Profile',
            line_color='rgb(255,255,255)',
            fillcolor='rgba(255,255,255,0.3)'
        ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 9]
                )),
            showlegend=True,
            title="Rainbow Numerology Radar Chart"
        )
        
        return fig
```

## Web開發套件

### Flask 擴展

#### flask-rainbow-numerology
```python
"""
Flask 彩虹靈數擴展
"""
from flask import Flask, render_template, request, jsonify
from flask_wtf import FlaskForm
from wtforms import StringField, DateField, SubmitField
from wtforms.validators import DataRequired

class NumerologyForm(FlaskForm):
    """彩虹靈數表單"""
    name = StringField('Full Name', validators=[DataRequired()])
    birth_date = DateField('Birth Date', validators=[DataRequired()])
    submit = SubmitField('Calculate')

class FlaskRainbowNumerology:
    """Flask 彩虹靈數應用"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化Flask應用"""
        app.config.setdefault('SECRET_KEY', 'rainbow-numerology-secret')
        
        @app.route('/')
        def index():
            form = NumerologyForm()
            return render_template('index.html', form=form)
        
        @app.route('/calculate', methods=['POST'])
        def calculate():
            form = NumerologyForm()
            if form.validate_on_submit():
                # 執行計算
                calculator = RainbowNumerologyCore()
                result = calculator.calculate_all(
                    form.name.data, 
                    form.birth_date.data
                )
                return jsonify(result)
            return jsonify({'error': 'Invalid input'})

# 使用示例
app = Flask(__name__)
rainbow = FlaskRainbowNumerology(app)
```

### Streamlit 應用

#### streamlit-rainbow-numerology
```python
"""
Streamlit 彩虹靈數應用
"""
import streamlit as st
import pandas as pd
from datetime import date

class StreamlitRainbowApp:
    """Streamlit 彩虹靈數應用"""
    
    def __init__(self):
        self.calculator = RainbowNumerologyCore()
        self.charts = InteractiveRainbowCharts()
    
    def run(self):
        """運行Streamlit應用"""
        st.set_page_config(
            page_title="Rainbow Numerology Calculator",
            page_icon="🌈",
            layout="wide"
        )
        
        st.title("🌈 Rainbow Numerology Calculator")
        
        # 側邊欄輸入
        with st.sidebar:
            st.header("Personal Information")
            name = st.text_input("Full Name")
            birth_date = st.date_input("Birth Date", max_value=date.today())
            
            if st.button("Calculate"):
                if name and birth_date:
                    self.display_results(name, birth_date)
    
    def display_results(self, name, birth_date):
        """顯示計算結果"""
        # 計算數字
        results = self.calculator.calculate_all(name, birth_date)
        
        # 顯示基本結果
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Life Number", results['life_number'])
            st.color_picker("Life Color", 
                          self.calculator.color_map[results['life_number']]['hex'])
        
        with col2:
            st.metric("Personality Number", results['personality_number'])
            st.color_picker("Personality Color", 
                          self.calculator.color_map[results['personality_number']]['hex'])
        
        with col3:
            st.metric("Soul Number", results['soul_number'])
            st.color_picker("Soul Color", 
                          self.calculator.color_map[results['soul_number']]['hex'])
        
        # 顯示3D視覺化
        fig = self.charts.create_3d_energy_sphere(
            results['life_number'],
            results['personality_number'],
            results['soul_number']
        )
        st.plotly_chart(fig, use_container_width=True)

# 運行應用
if __name__ == "__main__":
    app = StreamlitRainbowApp()
    app.run()
```

## 數據處理套件

### Pandas 擴展

#### pandas-numerology
```python
"""
Pandas 彩虹靈數擴展
"""
import pandas as pd
from pandas.api.extensions import register_dataframe_accessor

@register_dataframe_accessor("numerology")
class NumerologyAccessor:
    """Pandas DataFrame 彩虹靈數訪問器"""
    
    def __init__(self, pandas_obj):
        self._obj = pandas_obj
        self.calculator = RainbowNumerologyCore()
    
    def calculate_life_numbers(self, date_column):
        """批量計算生命數字"""
        return self._obj[date_column].apply(
            lambda x: self.calculator.calculate_life_number(x)
        )
    
    def calculate_personality_numbers(self, name_column):
        """批量計算個性數字"""
        return self._obj[name_column].apply(
            lambda x: self.calculator.calculate_personality_number(x)
        )
    
    def add_all_numbers(self, name_column, date_column):
        """添加所有數字到DataFrame"""
        df = self._obj.copy()
        df['life_number'] = self.calculate_life_numbers(date_column)
        df['personality_number'] = self.calculate_personality_numbers(name_column)
        df['soul_number'] = self._obj[name_column].apply(
            lambda x: self.calculator.calculate_soul_number(x)
        )
        return df

# 使用示例
df = pd.DataFrame({
    'name': ['John Smith', 'Mary Johnson', 'David Brown'],
    'birth_date': ['1985-03-15', '1990-07-22', '1978-11-08']
})

# 使用擴展功能
df_with_numbers = df.numerology.add_all_numbers('name', 'birth_date')
```

## 機器學習套件

### Scikit-learn 整合

#### numerology-ml
```python
"""
彩虹靈數機器學習套件
"""
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.preprocessing import LabelEncoder
import numpy as np

class NumerologyFeatureExtractor(BaseEstimator, TransformerMixin):
    """彩虹靈數特徵提取器"""
    
    def __init__(self):
        self.calculator = RainbowNumerologyCore()
        self.label_encoder = LabelEncoder()
    
    def fit(self, X, y=None):
        """訓練特徵提取器"""
        return self
    
    def transform(self, X):
        """轉換數據為數字特徵"""
        features = []
        
        for _, row in X.iterrows():
            name = row['name']
            birth_date = row['birth_date']
            
            # 提取基本數字
            life_num = self.calculator.calculate_life_number(birth_date)
            personality_num = self.calculator.calculate_personality_number(name)
            soul_num = self.calculator.calculate_soul_number(name)
            
            # 創建特徵向量
            feature_vector = [
                life_num, personality_num, soul_num,
                # 添加組合特徵
                life_num + personality_num,
                life_num * personality_num,
                abs(life_num - personality_num)
            ]
            
            features.append(feature_vector)
        
        return np.array(features)

class NumerologyCluster:
    """彩虹靈數聚類分析"""
    
    def __init__(self, n_clusters=9):
        from sklearn.cluster import KMeans
        self.kmeans = KMeans(n_clusters=n_clusters)
        self.feature_extractor = NumerologyFeatureExtractor()
    
    def fit_predict(self, data):
        """聚類分析"""
        features = self.feature_extractor.transform(data)
        clusters = self.kmeans.fit_predict(features)
        return clusters
    
    def analyze_clusters(self, data, clusters):
        """分析聚類結果"""
        analysis = {}
        
        for cluster_id in np.unique(clusters):
            cluster_data = data[clusters == cluster_id]
            
            # 計算聚類統計
            life_numbers = [self.feature_extractor.calculator.calculate_life_number(date) 
                          for date in cluster_data['birth_date']]
            
            analysis[cluster_id] = {
                'size': len(cluster_data),
                'dominant_life_number': max(set(life_numbers), key=life_numbers.count),
                'average_life_number': np.mean(life_numbers)
            }
        
        return analysis
```

## 測試套件

### pytest 擴展

#### pytest-numerology
```python
"""
彩虹靈數測試套件
"""
import pytest
from datetime import date

class NumerologyTestFixtures:
    """彩虹靈數測試固件"""
    
    @pytest.fixture
    def calculator(self):
        """計算器固件"""
        return RainbowNumerologyCore()
    
    @pytest.fixture
    def sample_data(self):
        """樣本數據固件"""
        return [
            {'name': 'John Smith', 'birth_date': '1985-03-15', 'expected_life': 5},
            {'name': 'Mary Johnson', 'birth_date': '1990-07-22', 'expected_life': 4},
            {'name': 'David Brown', 'birth_date': '1978-11-08', 'expected_life': 8}
        ]
    
    @pytest.fixture
    def color_validator(self):
        """色彩驗證器固件"""
        def validate_color(number, expected_color):
            calculator = RainbowNumerologyCore()
            color_info = calculator.color_map.get(number)
            return color_info and color_info['hex'] == expected_color
        return validate_color

# 測試用例
class TestRainbowNumerology(NumerologyTestFixtures):
    """彩虹靈數測試類"""
    
    def test_life_number_calculation(self, calculator, sample_data):
        """測試生命數字計算"""
        for person in sample_data:
            result = calculator.calculate_life_number(person['birth_date'])
            assert result == person['expected_life']
    
    def test_color_mapping(self, calculator, color_validator):
        """測試色彩對應"""
        assert color_validator(1, '#FF0000')  # 紅色
        assert color_validator(4, '#00FF00')  # 綠色
        assert color_validator(9, '#FFD700')  # 金色
    
    @pytest.mark.parametrize("number,expected_range", [
        (1, (1, 9)),
        (11, (11, 11)),  # 主數
        (22, (22, 22)),  # 主數
    ])
    def test_number_ranges(self, calculator, number, expected_range):
        """測試數字範圍"""
        assert expected_range[0] <= number <= expected_range[1]
```

## 部署和分發

### PyPI 套件結構

```
rainbow-numerology/
├── setup.py
├── README.md
├── LICENSE
├── requirements.txt
├── rainbow_numerology/
│   ├── __init__.py
│   ├── core.py
│   ├── calculators.py
│   ├── visualizers.py
│   └── utils.py
├── tests/
│   ├── __init__.py
│   ├── test_core.py
│   └── test_calculators.py
└── examples/
    ├── basic_usage.py
    ├── web_app.py
    └── data_analysis.py
```

### setup.py 配置

```python
from setuptools import setup, find_packages

setup(
    name="rainbow-numerology",
    version="1.0.0",
    author="Rainbow Numerology Team",
    author_email="<EMAIL>",
    description="A comprehensive Python library for Rainbow Numerology calculations",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    url="https://github.com/rainbow-numerology/python-library",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.8",
    install_requires=[
        "numpy>=1.20.0",
        "pandas>=1.3.0",
        "matplotlib>=3.4.0",
        "plotly>=5.0.0",
        "streamlit>=1.0.0",
        "flask>=2.0.0",
    ],
    extras_require={
        "dev": ["pytest>=6.0", "black>=21.0", "flake8>=3.9"],
        "ml": ["scikit-learn>=1.0", "tensorflow>=2.6"],
        "web": ["flask>=2.0", "streamlit>=1.0", "fastapi>=0.70"],
    },
    entry_points={
        "console_scripts": [
            "rainbow-calc=rainbow_numerology.cli:main",
        ],
    },
)
```

## 社群資源

### GitHub 資源庫

1. **rainbow-numerology/core** - 核心計算庫
2. **rainbow-numerology/web-tools** - Web應用工具
3. **rainbow-numerology/datasets** - 公開數據集
4. **rainbow-numerology/research** - 研究論文和分析

### 文檔和教程

- **官方文檔**: https://rainbow-numerology.readthedocs.io
- **API參考**: https://api.rainbow-numerology.com
- **教程集合**: https://tutorials.rainbow-numerology.com
- **範例代碼**: https://examples.rainbow-numerology.com

## 相關連結

- [[00 Python工具與資源總覽]]
- [[01 彩虹靈數計算器]]
- [[02 色彩視覺化工具]]
- [[06 機器學習應用]]
