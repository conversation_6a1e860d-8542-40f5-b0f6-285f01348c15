# 彩虹靈數計算器

## 概述

彩虹靈數計算器是使用Python開發的核心工具，能夠自動化計算各種數字類型，提高準確性和效率。本文將詳細介紹如何設計和實現一個完整的計算器系統。

## 基礎計算器架構

### 核心類設計

```python
class RainbowNumerologyCalculator:
    """彩虹靈數計算器主類"""
    
    def __init__(self):
        self.letter_values = self._init_letter_values()
        self.vowels = set('AEIOU')
        self.consonants = set('BCDFGHJKLMNPQRSTVWXYZ')
    
    def _init_letter_values(self):
        """初始化字母數值對應表"""
        values = {}
        for i, letter in enumerate('ABCDEFGHI', 1):
            values[letter] = i
        for i, letter in enumerate('JKLMNOPQR', 1):
            values[letter] = i
        for i, letter in enumerate('STUVWXYZ', 1):
            values[letter] = i
        return values
    
    def reduce_to_single_digit(self, number):
        """將數字簡化為個位數"""
        while number > 9 and number not in [11, 22, 33]:
            number = sum(int(digit) for digit in str(number))
        return number
```

### 生命數字計算

```python
def calculate_life_number(self, birth_date):
    """
    計算生命數字
    
    Args:
        birth_date: datetime.date 對象或 'YYYY-MM-DD' 格式字串
    
    Returns:
        int: 生命數字 (1-9 或 11, 22, 33)
    """
    if isinstance(birth_date, str):
        birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
    
    # 將日期轉換為數字字串
    date_string = birth_date.strftime('%Y%m%d')
    
    # 計算所有數字的總和
    total = sum(int(digit) for digit in date_string)
    
    # 簡化為個位數
    return self.reduce_to_single_digit(total)

# 使用示例
calculator = RainbowNumerologyCalculator()
life_number = calculator.calculate_life_number('1985-03-15')
print(f"生命數字: {life_number}")
```

### 個性數字計算

```python
def calculate_personality_number(self, full_name):
    """
    計算個性數字（基於子音）
    
    Args:
        full_name: str 完整姓名
    
    Returns:
        int: 個性數字 (1-9 或 11, 22, 33)
    """
    # 清理姓名，只保留字母
    clean_name = ''.join(char.upper() for char in full_name if char.isalpha())
    
    # 計算子音的數值總和
    consonant_sum = 0
    for char in clean_name:
        if char in self.consonants:
            consonant_sum += self.letter_values[char]
    
    return self.reduce_to_single_digit(consonant_sum)

# 使用示例
personality_number = calculator.calculate_personality_number('John Smith')
print(f"個性數字: {personality_number}")
```

### 靈魂數字計算

```python
def calculate_soul_number(self, full_name):
    """
    計算靈魂數字（基於母音）
    
    Args:
        full_name: str 完整姓名
    
    Returns:
        int: 靈魂數字 (1-9 或 11, 22, 33)
    """
    clean_name = ''.join(char.upper() for char in full_name if char.isalpha())
    
    # 處理Y字母的特殊情況
    processed_name = self._process_y_letters(clean_name)
    
    # 計算母音的數值總和
    vowel_sum = 0
    for char in processed_name:
        if char in self.vowels:
            vowel_sum += self.letter_values[char]
    
    return self.reduce_to_single_digit(vowel_sum)

def _process_y_letters(self, name):
    """處理Y字母的母音/子音判斷"""
    # 簡化版本：Y在母音後視為子音，否則視為母音
    processed = []
    for i, char in enumerate(name):
        if char == 'Y':
            if i > 0 and name[i-1] in self.vowels:
                # Y在母音後，視為子音
                processed.append('Y_CONSONANT')
            else:
                # Y視為母音
                processed.append('Y')
        else:
            processed.append(char)
    return ''.join(processed).replace('Y_CONSONANT', '')
```

## 進階功能實現

### 批量計算功能

```python
def batch_calculate(self, data_list):
    """
    批量計算多個人的數字
    
    Args:
        data_list: list of dict, 每個dict包含 'name' 和 'birth_date'
    
    Returns:
        list of dict: 包含所有計算結果的列表
    """
    results = []
    
    for person in data_list:
        try:
            result = {
                'name': person['name'],
                'birth_date': person['birth_date'],
                'life_number': self.calculate_life_number(person['birth_date']),
                'personality_number': self.calculate_personality_number(person['name']),
                'soul_number': self.calculate_soul_number(person['name'])
            }
            
            # 計算使命數字（生命數字 + 個性數字）
            result['destiny_number'] = self.reduce_to_single_digit(
                result['life_number'] + result['personality_number']
            )
            
            results.append(result)
            
        except Exception as e:
            results.append({
                'name': person.get('name', 'Unknown'),
                'error': str(e)
            })
    
    return results

# 使用示例
data = [
    {'name': 'John Smith', 'birth_date': '1985-03-15'},
    {'name': 'Mary Johnson', 'birth_date': '1990-07-22'},
    {'name': 'David Brown', 'birth_date': '1978-11-08'}
]

results = calculator.batch_calculate(data)
for result in results:
    if 'error' not in result:
        print(f"{result['name']}: 生命數字={result['life_number']}, "
              f"個性數字={result['personality_number']}")
```

### 數字驗證功能

```python
class NumberValidator:
    """數字計算結果驗證器"""
    
    @staticmethod
    def validate_life_number(birth_date, expected_result):
        """驗證生命數字計算結果"""
        calculator = RainbowNumerologyCalculator()
        actual_result = calculator.calculate_life_number(birth_date)
        return actual_result == expected_result
    
    @staticmethod
    def validate_calculation_steps(birth_date):
        """顯示計算步驟以供驗證"""
        if isinstance(birth_date, str):
            birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
        
        date_string = birth_date.strftime('%Y%m%d')
        digits = [int(d) for d in date_string]
        
        steps = []
        steps.append(f"出生日期: {birth_date}")
        steps.append(f"數字字串: {date_string}")
        steps.append(f"各位數字: {' + '.join(map(str, digits))}")
        
        total = sum(digits)
        steps.append(f"總和: {total}")
        
        # 顯示簡化過程
        while total > 9 and total not in [11, 22, 33]:
            digits = [int(d) for d in str(total)]
            steps.append(f"簡化: {' + '.join(map(str, digits))} = {sum(digits)}")
            total = sum(digits)
        
        steps.append(f"最終結果: {total}")
        return steps

# 使用示例
validator = NumberValidator()
steps = validator.validate_calculation_steps('1985-03-15')
for step in steps:
    print(step)
```

## 色彩對應功能

```python
class ColorMapper:
    """數字色彩對應器"""
    
    def __init__(self):
        self.color_map = {
            1: {'name': '紅色', 'hex': '#FF0000', 'rgb': (255, 0, 0)},
            2: {'name': '橙色', 'hex': '#FF8000', 'rgb': (255, 128, 0)},
            3: {'name': '黃色', 'hex': '#FFFF00', 'rgb': (255, 255, 0)},
            4: {'name': '綠色', 'hex': '#00FF00', 'rgb': (0, 255, 0)},
            5: {'name': '藍色', 'hex': '#0080FF', 'rgb': (0, 128, 255)},
            6: {'name': '靛色', 'hex': '#4000FF', 'rgb': (64, 0, 255)},
            7: {'name': '紫色', 'hex': '#8000FF', 'rgb': (128, 0, 255)},
            8: {'name': '粉色', 'hex': '#FF80FF', 'rgb': (255, 128, 255)},
            9: {'name': '金色', 'hex': '#FFD700', 'rgb': (255, 215, 0)}
        }
    
    def get_color_info(self, number):
        """獲取數字對應的色彩信息"""
        return self.color_map.get(number, None)
    
    def get_color_palette(self, numbers):
        """獲取數字列表對應的色彩調色板"""
        palette = []
        for num in numbers:
            color_info = self.get_color_info(num)
            if color_info:
                palette.append(color_info)
        return palette

# 整合到主計算器中
class EnhancedRainbowCalculator(RainbowNumerologyCalculator):
    """增強版彩虹靈數計算器"""
    
    def __init__(self):
        super().__init__()
        self.color_mapper = ColorMapper()
    
    def get_complete_profile(self, name, birth_date):
        """獲取完整的數字檔案"""
        profile = {
            'name': name,
            'birth_date': birth_date,
            'life_number': self.calculate_life_number(birth_date),
            'personality_number': self.calculate_personality_number(name),
            'soul_number': self.calculate_soul_number(name)
        }
        
        # 添加色彩信息
        for key in ['life_number', 'personality_number', 'soul_number']:
            number = profile[key]
            color_info = self.color_mapper.get_color_info(number)
            profile[f'{key}_color'] = color_info
        
        return profile
```

## 錯誤處理和異常管理

```python
class NumerologyError(Exception):
    """彩虹靈數計算相關的自定義異常"""
    pass

class InvalidDateError(NumerologyError):
    """無效日期異常"""
    pass

class InvalidNameError(NumerologyError):
    """無效姓名異常"""
    pass

def safe_calculate_life_number(self, birth_date):
    """安全的生命數字計算（包含錯誤處理）"""
    try:
        if isinstance(birth_date, str):
            # 驗證日期格式
            try:
                birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
            except ValueError:
                raise InvalidDateError(f"無效的日期格式: {birth_date}")
        
        # 檢查日期合理性
        if birth_date > date.today():
            raise InvalidDateError("出生日期不能是未來日期")
        
        if birth_date.year < 1900:
            raise InvalidDateError("出生年份不能早於1900年")
        
        return self.calculate_life_number(birth_date)
        
    except Exception as e:
        raise NumerologyError(f"計算生命數字時發生錯誤: {str(e)}")
```

## 性能優化

```python
from functools import lru_cache
import time

class OptimizedCalculator(RainbowNumerologyCalculator):
    """性能優化版計算器"""
    
    @lru_cache(maxsize=1000)
    def cached_calculate_life_number(self, birth_date_str):
        """使用緩存的生命數字計算"""
        return self.calculate_life_number(birth_date_str)
    
    @lru_cache(maxsize=1000)
    def cached_calculate_personality_number(self, name):
        """使用緩存的個性數字計算"""
        return self.calculate_personality_number(name)
    
    def benchmark_calculation(self, test_data, iterations=1000):
        """性能基準測試"""
        start_time = time.time()
        
        for _ in range(iterations):
            for person in test_data:
                self.cached_calculate_life_number(person['birth_date'])
                self.cached_calculate_personality_number(person['name'])
        
        end_time = time.time()
        total_time = end_time - start_time
        
        return {
            'total_time': total_time,
            'average_time': total_time / (iterations * len(test_data)),
            'calculations_per_second': (iterations * len(test_data)) / total_time
        }
```

## 單元測試

```python
import unittest
from datetime import date

class TestRainbowNumerologyCalculator(unittest.TestCase):
    
    def setUp(self):
        self.calculator = RainbowNumerologyCalculator()
    
    def test_life_number_calculation(self):
        """測試生命數字計算"""
        # 測試已知結果
        self.assertEqual(self.calculator.calculate_life_number('1985-03-15'), 5)
        self.assertEqual(self.calculator.calculate_life_number('1992-11-22'), 9)
    
    def test_personality_number_calculation(self):
        """測試個性數字計算"""
        self.assertEqual(self.calculator.calculate_personality_number('John Smith'), 2)
        self.assertEqual(self.calculator.calculate_personality_number('Mary Johnson'), 9)
    
    def test_number_reduction(self):
        """測試數字簡化"""
        self.assertEqual(self.calculator.reduce_to_single_digit(23), 5)
        self.assertEqual(self.calculator.reduce_to_single_digit(11), 11)  # 主數保留
        self.assertEqual(self.calculator.reduce_to_single_digit(99), 9)
    
    def test_invalid_input(self):
        """測試無效輸入處理"""
        with self.assertRaises(Exception):
            self.calculator.calculate_life_number('invalid-date')

if __name__ == '__main__':
    unittest.main()
```

## 命令行界面

```python
import argparse
import sys

def create_cli():
    """創建命令行界面"""
    parser = argparse.ArgumentParser(description='彩虹靈數計算器')
    
    parser.add_argument('--name', type=str, help='完整姓名')
    parser.add_argument('--birth-date', type=str, help='出生日期 (YYYY-MM-DD)')
    parser.add_argument('--batch', type=str, help='批量處理CSV文件路徑')
    parser.add_argument('--output', type=str, help='輸出文件路徑')
    
    return parser

def main():
    parser = create_cli()
    args = parser.parse_args()
    
    calculator = EnhancedRainbowCalculator()
    
    if args.batch:
        # 批量處理模式
        import pandas as pd
        df = pd.read_csv(args.batch)
        results = calculator.batch_calculate(df.to_dict('records'))
        
        if args.output:
            pd.DataFrame(results).to_csv(args.output, index=False)
        else:
            for result in results:
                print(result)
    
    elif args.name and args.birth_date:
        # 單個計算模式
        profile = calculator.get_complete_profile(args.name, args.birth_date)
        
        print(f"姓名: {profile['name']}")
        print(f"出生日期: {profile['birth_date']}")
        print(f"生命數字: {profile['life_number']} ({profile['life_number_color']['name']})")
        print(f"個性數字: {profile['personality_number']} ({profile['personality_number_color']['name']})")
        print(f"靈魂數字: {profile['soul_number']} ({profile['soul_number_color']['name']})")
    
    else:
        parser.print_help()

if __name__ == '__main__':
    main()
```

## 相關連結

- [[00 Python工具與資源總覽]]
- [[02 色彩視覺化工具]]
- [[../02 數字計算/00 數字計算總覽|數字計算總覽]]
- [[../04 實用應用/01 個人成長指導|個人成長指導]]
