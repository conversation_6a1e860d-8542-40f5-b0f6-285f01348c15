# 機器學習應用

## 概述

機器學習技術為彩虹靈數的研究和應用開啟了新的可能性。透過大數據分析、模式識別和預測建模，我們可以發現數字組合的深層規律，提供更精準的個人化指導。

## 應用領域

### 1. 模式識別
- **數字組合模式**：識別成功人士的數字特徵
- **職業匹配模式**：分析數字與職業成功的關聯
- **關係相容模式**：發現和諧關係的數字組合
- **健康狀態模式**：探索數字與健康的關聯性

### 2. 預測建模
- **人生發展預測**：基於數字預測個人發展軌跡
- **關係成功預測**：預測關係的穩定性和成功率
- **職業適配預測**：預測個人在不同職業的成功可能
- **健康風險預測**：基於數字組合預測健康風險

### 3. 個性化推薦
- **色彩療法推薦**：根據個人狀態推薦最適合的色彩
- **冥想練習推薦**：推薦個性化的冥想和修練方法
- **生活指導推薦**：提供基於數字的生活建議
- **學習路徑推薦**：推薦個人化的成長學習路徑

## 核心技術架構

### 數據預處理

```python
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from datetime import datetime, date

class NumerologyDataProcessor:
    """彩虹靈數數據預處理器"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_names = []
    
    def extract_numerology_features(self, df):
        """提取彩虹靈數特徵"""
        features = pd.DataFrame()
        
        # 基本數字特徵
        features['life_number'] = df.apply(
            lambda row: self.calculate_life_number(row['birth_date']), axis=1
        )
        features['personality_number'] = df.apply(
            lambda row: self.calculate_personality_number(row['name']), axis=1
        )
        features['soul_number'] = df.apply(
            lambda row: self.calculate_soul_number(row['name']), axis=1
        )
        
        # 組合特徵
        features['life_personality_sum'] = features['life_number'] + features['personality_number']
        features['life_personality_diff'] = abs(features['life_number'] - features['personality_number'])
        features['life_soul_harmony'] = (features['life_number'] == features['soul_number']).astype(int)
        
        # 色彩特徵
        features['dominant_color_group'] = features['life_number'].apply(self.get_color_group)
        features['color_balance_score'] = features.apply(self.calculate_color_balance, axis=1)
        
        # 脈輪特徵
        features['chakra_alignment'] = features.apply(self.calculate_chakra_alignment, axis=1)
        
        return features
    
    def calculate_life_number(self, birth_date):
        """計算生命數字"""
        if isinstance(birth_date, str):
            birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
        
        date_string = birth_date.strftime('%Y%m%d')
        total = sum(int(digit) for digit in date_string)
        
        while total > 9 and total not in [11, 22, 33]:
            total = sum(int(digit) for digit in str(total))
        
        return total
    
    def get_color_group(self, number):
        """獲取色彩組別"""
        if number in [1, 2, 3]:
            return 'warm'  # 暖色系
        elif number in [4, 5, 6, 7]:
            return 'cool'  # 冷色系
        else:
            return 'special'  # 特殊色彩
    
    def calculate_color_balance(self, row):
        """計算色彩平衡分數"""
        numbers = [row['life_number'], row['personality_number'], row['soul_number']]
        warm_count = sum(1 for n in numbers if n in [1, 2, 3])
        cool_count = sum(1 for n in numbers if n in [4, 5, 6, 7])
        special_count = sum(1 for n in numbers if n in [8, 9])
        
        # 計算平衡分數（越接近均勻分佈分數越高）
        total = len(numbers)
        balance_score = 1 - abs(warm_count - cool_count) / total
        return balance_score
```

### 聚類分析

```python
from sklearn.cluster import KMeans, DBSCAN
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
import seaborn as sns

class NumerologyClusterAnalysis:
    """彩虹靈數聚類分析"""
    
    def __init__(self, n_clusters=9):
        self.n_clusters = n_clusters
        self.kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        self.pca = PCA(n_components=2)
        self.cluster_labels = None
        self.cluster_centers = None
    
    def fit_predict(self, features):
        """執行聚類分析"""
        # 標準化特徵
        features_scaled = StandardScaler().fit_transform(features)
        
        # K-means聚類
        self.cluster_labels = self.kmeans.fit_predict(features_scaled)
        self.cluster_centers = self.kmeans.cluster_centers_
        
        return self.cluster_labels
    
    def analyze_clusters(self, features, labels, target_variable=None):
        """分析聚類結果"""
        analysis_results = {}
        
        for cluster_id in range(self.n_clusters):
            cluster_mask = (self.cluster_labels == cluster_id)
            cluster_features = features[cluster_mask]
            
            analysis = {
                'size': cluster_mask.sum(),
                'life_number_mode': cluster_features['life_number'].mode().iloc[0],
                'personality_number_mode': cluster_features['personality_number'].mode().iloc[0],
                'dominant_color_group': cluster_features['dominant_color_group'].mode().iloc[0],
                'avg_color_balance': cluster_features['color_balance_score'].mean(),
                'characteristics': self.describe_cluster_characteristics(cluster_features)
            }
            
            if target_variable is not None:
                cluster_target = target_variable[cluster_mask]
                analysis['target_distribution'] = cluster_target.value_counts().to_dict()
            
            analysis_results[cluster_id] = analysis
        
        return analysis_results
    
    def visualize_clusters(self, features):
        """視覺化聚類結果"""
        # PCA降維
        features_2d = self.pca.fit_transform(StandardScaler().fit_transform(features))
        
        # 創建圖表
        plt.figure(figsize=(12, 8))
        scatter = plt.scatter(features_2d[:, 0], features_2d[:, 1], 
                            c=self.cluster_labels, cmap='rainbow', alpha=0.7)
        plt.colorbar(scatter)
        plt.title('Rainbow Numerology Clusters (PCA Visualization)')
        plt.xlabel('First Principal Component')
        plt.ylabel('Second Principal Component')
        
        # 標記聚類中心
        centers_2d = self.pca.transform(self.cluster_centers)
        plt.scatter(centers_2d[:, 0], centers_2d[:, 1], 
                   c='black', marker='x', s=200, linewidths=3)
        
        plt.show()
```

### 預測建模

```python
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, mean_squared_error, r2_score

class NumerologyPredictiveModel:
    """彩虹靈數預測模型"""
    
    def __init__(self, model_type='classification'):
        self.model_type = model_type
        if model_type == 'classification':
            self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        else:
            self.model = GradientBoostingRegressor(n_estimators=100, random_state=42)
        
        self.feature_importance = None
        self.is_fitted = False
    
    def train_career_success_model(self, features, career_success_labels):
        """訓練職業成功預測模型"""
        X_train, X_test, y_train, y_test = train_test_split(
            features, career_success_labels, test_size=0.2, random_state=42
        )
        
        # 訓練模型
        self.model.fit(X_train, y_train)
        self.is_fitted = True
        
        # 評估模型
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        
        # 交叉驗證
        cv_scores = cross_val_score(self.model, features, career_success_labels, cv=5)
        
        # 特徵重要性
        self.feature_importance = pd.DataFrame({
            'feature': features.columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        results = {
            'train_score': train_score,
            'test_score': test_score,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'feature_importance': self.feature_importance
        }
        
        return results
    
    def predict_relationship_compatibility(self, person1_features, person2_features):
        """預測關係相容性"""
        # 創建關係特徵
        relationship_features = self.create_relationship_features(
            person1_features, person2_features
        )
        
        if not self.is_fitted:
            raise ValueError("Model must be trained before making predictions")
        
        compatibility_score = self.model.predict_proba([relationship_features])[0][1]
        return compatibility_score
    
    def create_relationship_features(self, person1, person2):
        """創建關係特徵向量"""
        features = []
        
        # 數字差異特徵
        features.append(abs(person1['life_number'] - person2['life_number']))
        features.append(abs(person1['personality_number'] - person2['personality_number']))
        features.append(abs(person1['soul_number'] - person2['soul_number']))
        
        # 數字和諧特徵
        features.append(int(person1['life_number'] == person2['life_number']))
        features.append(int(person1['personality_number'] == person2['personality_number']))
        features.append(int(person1['soul_number'] == person2['soul_number']))
        
        # 色彩組合特徵
        features.append(int(person1['dominant_color_group'] == person2['dominant_color_group']))
        features.append((person1['color_balance_score'] + person2['color_balance_score']) / 2)
        
        # 互補特徵
        complementary_pairs = [(1, 4), (2, 5), (3, 7), (6, 9)]
        complementary_score = 0
        for pair in complementary_pairs:
            if (person1['life_number'] in pair and person2['life_number'] in pair and 
                person1['life_number'] != person2['life_number']):
                complementary_score += 1
        features.append(complementary_score)
        
        return features
```

### 深度學習應用

```python
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam

class NumerologyDeepLearning:
    """彩虹靈數深度學習模型"""
    
    def __init__(self, input_dim, output_dim, task_type='regression'):
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.task_type = task_type
        self.model = self.build_model()
        self.history = None
    
    def build_model(self):
        """構建深度學習模型"""
        model = Sequential([
            Dense(128, activation='relu', input_shape=(self.input_dim,)),
            BatchNormalization(),
            Dropout(0.3),
            
            Dense(64, activation='relu'),
            BatchNormalization(),
            Dropout(0.3),
            
            Dense(32, activation='relu'),
            BatchNormalization(),
            Dropout(0.2),
            
            Dense(16, activation='relu'),
            
            Dense(self.output_dim, 
                  activation='softmax' if self.task_type == 'classification' else 'linear')
        ])
        
        # 編譯模型
        if self.task_type == 'classification':
            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='categorical_crossentropy',
                metrics=['accuracy']
            )
        else:
            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='mse',
                metrics=['mae']
            )
        
        return model
    
    def train_personality_prediction_model(self, features, personality_traits, epochs=100):
        """訓練個性特質預測模型"""
        # 分割數據
        X_train, X_test, y_train, y_test = train_test_split(
            features, personality_traits, test_size=0.2, random_state=42
        )
        
        # 訓練模型
        self.history = self.model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=epochs,
            batch_size=32,
            verbose=1,
            callbacks=[
                tf.keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
                tf.keras.callbacks.ReduceLROnPlateau(patience=5, factor=0.5)
            ]
        )
        
        # 評估模型
        train_loss = self.model.evaluate(X_train, y_train, verbose=0)
        test_loss = self.model.evaluate(X_test, y_test, verbose=0)
        
        return {
            'train_loss': train_loss,
            'test_loss': test_loss,
            'history': self.history.history
        }
    
    def predict_life_path_guidance(self, personal_features):
        """預測人生路徑指導"""
        prediction = self.model.predict([personal_features])
        
        # 解釋預測結果
        guidance = self.interpret_prediction(prediction[0])
        
        return guidance
    
    def interpret_prediction(self, prediction):
        """解釋預測結果"""
        guidance_categories = [
            'Career Development', 'Relationship Harmony', 'Health & Wellness',
            'Spiritual Growth', 'Creative Expression', 'Financial Stability',
            'Family Relationships', 'Personal Freedom', 'Service to Others'
        ]
        
        guidance = {}
        for i, category in enumerate(guidance_categories):
            if i < len(prediction):
                guidance[category] = {
                    'score': float(prediction[i]),
                    'priority': 'High' if prediction[i] > 0.7 else 'Medium' if prediction[i] > 0.4 else 'Low'
                }
        
        return guidance
```

### 自然語言處理應用

```python
import nltk
from textblob import TextBlob
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB

class NumerologyNLP:
    """彩虹靈數自然語言處理"""
    
    def __init__(self):
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self.sentiment_classifier = MultinomialNB()
        self.is_trained = False
    
    def analyze_name_sentiment(self, name):
        """分析姓名的情感傾向"""
        blob = TextBlob(name)
        sentiment = blob.sentiment
        
        return {
            'polarity': sentiment.polarity,  # -1 (negative) to 1 (positive)
            'subjectivity': sentiment.subjectivity,  # 0 (objective) to 1 (subjective)
            'interpretation': self.interpret_name_sentiment(sentiment)
        }
    
    def interpret_name_sentiment(self, sentiment):
        """解釋姓名情感分析結果"""
        polarity = sentiment.polarity
        subjectivity = sentiment.subjectivity
        
        if polarity > 0.1:
            polarity_desc = "Positive energy"
        elif polarity < -0.1:
            polarity_desc = "Challenging energy"
        else:
            polarity_desc = "Neutral energy"
        
        if subjectivity > 0.5:
            subjectivity_desc = "Highly personal and emotional"
        else:
            subjectivity_desc = "More objective and factual"
        
        return f"{polarity_desc}, {subjectivity_desc}"
    
    def generate_personalized_guidance(self, numerology_profile, life_situation):
        """生成個性化指導"""
        # 基於數字特徵生成指導模板
        guidance_templates = {
            1: "As a natural leader with life number 1, focus on {focus_area}. Your red energy suggests {action_advice}.",
            2: "With your cooperative nature (life number 2), consider {focus_area}. Orange energy brings {emotional_advice}.",
            3: "Your creative spirit (life number 3) shines in {focus_area}. Yellow energy encourages {expression_advice}.",
            4: "Your stable foundation (life number 4) supports {focus_area}. Green energy promotes {growth_advice}.",
            5: "Your adventurous soul (life number 5) seeks {focus_area}. Blue energy enhances {communication_advice}.",
            6: "Your nurturing heart (life number 6) guides {focus_area}. Indigo energy deepens {intuition_advice}.",
            7: "Your spiritual quest (life number 7) leads to {focus_area}. Violet energy opens {wisdom_advice}.",
            8: "Your ambitious drive (life number 8) achieves {focus_area}. Pink energy balances {power_advice}.",
            9: "Your humanitarian spirit (life number 9) serves {focus_area}. Gold energy illuminates {service_advice}."
        }
        
        life_number = numerology_profile['life_number']
        template = guidance_templates.get(life_number, "Your unique path leads to {focus_area}.")
        
        # 根據生活情況填充模板
        guidance = self.fill_guidance_template(template, life_situation, numerology_profile)
        
        return guidance
    
    def fill_guidance_template(self, template, situation, profile):
        """填充指導模板"""
        # 簡化版本的模板填充
        focus_areas = {
            'career': 'professional development and career advancement',
            'relationship': 'building meaningful connections and partnerships',
            'health': 'physical wellness and emotional balance',
            'spiritual': 'inner growth and spiritual awakening'
        }
        
        situation_type = self.classify_situation(situation)
        focus_area = focus_areas.get(situation_type, 'personal growth and self-discovery')
        
        # 根據數字特徵提供具體建議
        specific_advice = self.generate_specific_advice(profile, situation_type)
        
        return template.format(
            focus_area=focus_area,
            action_advice=specific_advice.get('action', 'taking decisive action'),
            emotional_advice=specific_advice.get('emotional', 'emotional harmony'),
            expression_advice=specific_advice.get('expression', 'creative expression'),
            growth_advice=specific_advice.get('growth', 'steady growth'),
            communication_advice=specific_advice.get('communication', 'clear communication'),
            intuition_advice=specific_advice.get('intuition', 'intuitive insights'),
            wisdom_advice=specific_advice.get('wisdom', 'deeper wisdom'),
            power_advice=specific_advice.get('power', 'compassionate leadership'),
            service_advice=specific_advice.get('service', 'selfless service')
        )
    
    def classify_situation(self, situation_text):
        """分類生活情況"""
        # 簡單的關鍵詞分類
        career_keywords = ['work', 'job', 'career', 'business', 'professional']
        relationship_keywords = ['love', 'relationship', 'partner', 'family', 'friend']
        health_keywords = ['health', 'wellness', 'stress', 'energy', 'balance']
        spiritual_keywords = ['spiritual', 'meditation', 'growth', 'purpose', 'meaning']
        
        situation_lower = situation_text.lower()
        
        if any(keyword in situation_lower for keyword in career_keywords):
            return 'career'
        elif any(keyword in situation_lower for keyword in relationship_keywords):
            return 'relationship'
        elif any(keyword in situation_lower for keyword in health_keywords):
            return 'health'
        elif any(keyword in situation_lower for keyword in spiritual_keywords):
            return 'spiritual'
        else:
            return 'general'
```

## 實際應用案例

### 案例1：職業匹配系統

```python
class CareerMatchingSystem:
    """職業匹配系統"""
    
    def __init__(self):
        self.model = NumerologyPredictiveModel('classification')
        self.career_database = self.load_career_database()
    
    def find_best_careers(self, personal_profile, top_n=5):
        """找出最適合的職業"""
        career_scores = []
        
        for career in self.career_database:
            compatibility_score = self.calculate_career_compatibility(
                personal_profile, career
            )
            career_scores.append((career['name'], compatibility_score))
        
        # 排序並返回前N個
        career_scores.sort(key=lambda x: x[1], reverse=True)
        return career_scores[:top_n]
    
    def calculate_career_compatibility(self, profile, career):
        """計算職業相容性"""
        # 基於數字特徵計算相容性分數
        life_number = profile['life_number']
        personality_number = profile['personality_number']
        
        # 職業數字特徵（預定義）
        career_numbers = career.get('preferred_numbers', [])
        
        compatibility = 0
        if life_number in career_numbers:
            compatibility += 0.4
        if personality_number in career_numbers:
            compatibility += 0.3
        
        # 色彩能量匹配
        color_match = self.calculate_color_energy_match(profile, career)
        compatibility += color_match * 0.3
        
        return compatibility
```

### 案例2：關係諮詢系統

```python
class RelationshipCounselingSystem:
    """關係諮詢系統"""
    
    def __init__(self):
        self.compatibility_model = NumerologyPredictiveModel('regression')
        self.guidance_generator = NumerologyNLP()
    
    def analyze_relationship(self, person1_profile, person2_profile):
        """分析關係相容性"""
        # 計算相容性分數
        compatibility_score = self.compatibility_model.predict_relationship_compatibility(
            person1_profile, person2_profile
        )
        
        # 分析潛在挑戰
        challenges = self.identify_potential_challenges(person1_profile, person2_profile)
        
        # 生成改善建議
        improvement_suggestions = self.generate_improvement_suggestions(
            person1_profile, person2_profile, challenges
        )
        
        return {
            'compatibility_score': compatibility_score,
            'challenges': challenges,
            'suggestions': improvement_suggestions,
            'color_harmony': self.analyze_color_harmony(person1_profile, person2_profile)
        }
```

## 模型評估和優化

### 性能指標

```python
def evaluate_model_performance(model, X_test, y_test, model_type='classification'):
    """評估模型性能"""
    predictions = model.predict(X_test)
    
    if model_type == 'classification':
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
        metrics = {
            'accuracy': accuracy_score(y_test, predictions),
            'precision': precision_score(y_test, predictions, average='weighted'),
            'recall': recall_score(y_test, predictions, average='weighted'),
            'f1_score': f1_score(y_test, predictions, average='weighted')
        }
    else:
        from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
        
        metrics = {
            'mse': mean_squared_error(y_test, predictions),
            'mae': mean_absolute_error(y_test, predictions),
            'r2_score': r2_score(y_test, predictions)
        }
    
    return metrics
```

### 模型解釋性

```python
import shap

class ModelExplainer:
    """模型解釋器"""
    
    def __init__(self, model, feature_names):
        self.model = model
        self.feature_names = feature_names
        self.explainer = shap.TreeExplainer(model)
    
    def explain_prediction(self, sample_features):
        """解釋單個預測"""
        shap_values = self.explainer.shap_values(sample_features)
        
        explanation = {}
        for i, feature_name in enumerate(self.feature_names):
            explanation[feature_name] = {
                'value': sample_features[i],
                'impact': shap_values[i],
                'importance': abs(shap_values[i])
            }
        
        return explanation
    
    def generate_feature_importance_plot(self, X_sample):
        """生成特徵重要性圖"""
        shap_values = self.explainer.shap_values(X_sample)
        shap.summary_plot(shap_values, X_sample, feature_names=self.feature_names)
```

## 相關連結

- [[00 Python工具與資源總覽]]
- [[01 彩虹靈數計算器]]
- [[03 數據分析與統計]]
- [[07 開源資源與套件]]
- [[../04 實用應用/00 實用應用總覽|實用應用總覽]]
